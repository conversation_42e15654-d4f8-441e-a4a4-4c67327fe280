# My Company Control Center (MC3) changelog

## [unreleased] 2024-07-25
### Changed
### Added
### Removed

## [0.17.0] 2025-06-24
### Changed
### Added
- Subscription management
- Payment management
- Payment providers (PayU)
- Payment webhooks
### Removed


## [0.16.0] 2025-05-30
### Changed
- docker prepared for production
### Added
- image to tenant
- invoice configuration
- invoice templates
### Removed


## [0.15.0] 2025-05-26
### Changed
- docker prepared for production
### Added
### Removed


## [0.14.2] 2025-05-19
### Changed
- composer update (max php 8.2 )
### Added
### Removed


## [0.14.1] 2025-05-19
### Changed
- composer update
### Added
### Removed


## [0.14.0] 2025-05-19
### Changed
### Added
- Charts to user panel
- SIMPLE_CHARTS as a system module
### Removed

## [0.13.2] 2025-05-15
### Changed
- GUS import improved and secured
- TradeDocs select the first trade doc type if only one exists
- Invoice correction is unpaid when created
### Added
### Removed

## [0.13.1] 2025-05-12
### Changed
- docker build updated to 8.3:latest (8.3.21)
- missing image to VCS
### Added
### Removed


## [0.13.0] 2025-05-12
### Changed
- partner CRUD removed the business type, the accounting type
- user Panel uses hash now
- login restriction: must be active, must have the tenant (if app), must be the God (if admin)
- Add error handling and logging for JPK XML generation
- code formatting
### Added
- Registration
### Removed

## [0.12.0] 2025-04-03
### Added
- Forms color themes, layout changed 

## [0.11.0] 2025-03-24
### Added
- Old invoices import
- Tenant export
### Fixed
- Creating new warehouse by admin makes it active by default now

## [0.10.0] 2025-03-18
### Added
- Purchase docs

## [0.9.1] 2025-03-05
### Added
- GUS Api 
### Changed
- New add product quick form
- Partner short name


## [0.9.0] 2025-02-28
### Added
- Visual redesign part 1. Added theme file

## [0.7.0] 2024-09-05
### Added
- Docker local environment

## [0.6.0] 2024-08-30
### Added
- Multi-domain support

## [0.5.0] 2024-07-25
### Added
- Warehouse module
- System modules management
- Versioning
